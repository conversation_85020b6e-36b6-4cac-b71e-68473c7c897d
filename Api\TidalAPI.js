import axios from "axios";
import { getCachedData, CACHE_GROUPS } from './CacheManager';

// Multiple endpoints for load balancing and rate limit bypass
const TIDAL_ENDPOINTS = [
  'https://dev-paxsenix.koyeb.app',
  // Add more endpoints here if available
  // 'https://backup-endpoint1.com',
  // 'https://backup-endpoint2.com',
];

let currentEndpointIndex = 0;

/**
 * Get current Tidal endpoint with rotation
 */
function getCurrentTidalEndpoint() {
  return TIDAL_ENDPOINTS[currentEndpointIndex];
}

/**
 * Rotate to next endpoint for load balancing
 */
function rotateEndpoint() {
  currentEndpointIndex = (currentEndpointIndex + 1) % TIDAL_ENDPOINTS.length;
  console.log(`Rotated to Tidal endpoint: ${getCurrentTidalEndpoint()}`);
}

/**
 * Search for songs on Tidal
 * @param {string} searchText - Search query
 * @param {number} page - Page number (not used by Tidal API but kept for consistency)
 * @param {number} limit - Limit (not used by Tidal API but kept for consistency)
 * @returns {Promise<Object>} Search results
 */
async function getTidalSearchSongData(searchText, page = 1, limit = 20) {
  const cacheKey = `tidal_search_${searchText}_page${page}_limit${limit}`;
  
  const fetchFunction = async () => {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${getCurrentTidalEndpoint()}/tidal/search?q=${encodeURIComponent(searchText)}`,
      headers: {
        'X-Forwarded-For': generateRandomIP(),
      },
      timeout: 10000
    };
    
    try {
      const response = await axios.request(config);

      // Transform Tidal response to match app format
      if (response.data && response.data.items && Array.isArray(response.data.items)) {
        const transformedResults = response.data.items.map(track => ({
          id: track.id,
          name: track.title,
          title: track.title,
          artist: track.artist?.name || (track.artists && track.artists[0]?.name) || 'Unknown Artist',
          artists: {
            primary: track.artists ? track.artists.map(artist => ({ name: artist.name })) : [{ name: 'Unknown Artist' }]
          },
          primary_artists_id: track.artist?.id || (track.artists && track.artists[0]?.id) || '',
          duration: track.duration || 0,
          image: track.album?.cover ? [
            { url: track.album.cover },
            { url: track.album.cover },
            { url: track.album.cover }
          ] : [],
          downloadUrl: [], // Will be populated when playing
          language: 'en', // Default for Tidal
          source: 'tidal',
          tidalUrl: track.url // Store original Tidal URL for streaming
        }));

        return {
          success: true,
          data: {
            results: transformedResults
          }
        };
      }

      return {
        success: false,
        message: 'No tracks found'
      };
    } catch (error) {
      console.error('Tidal search error:', error);
      return {
        success: false,
        error: error.message || 'Network error',
        message: 'Failed to search Tidal'
      };
    }
  };

  try {
    return await getCachedData(cacheKey, fetchFunction, 30, CACHE_GROUPS.SEARCH);
  } catch (error) {
    console.error(`Error getting Tidal search data for "${searchText}":`, error);
    return {
      success: false,
      error: error.message,
      message: 'Search failed'
    };
  }
}

/**
 * Get Tidal track details
 * @param {string} trackId - Tidal track ID
 * @returns {Promise<Object>} Track details
 */
async function getTidalTrackData(trackId) {
  const cacheKey = `tidal_track_${trackId}`;
  
  const fetchFunction = async () => {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${TIDAL_BASE_URL}/tidal/track?id=${trackId}`,
      headers: {},
      timeout: 10000
    };
    
    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(`Tidal track error for ID ${trackId}:`, error);
      throw error;
    }
  };

  try {
    return await getCachedData(cacheKey, fetchFunction, 60, CACHE_GROUPS.SONGS);
  } catch (error) {
    console.error(`Error getting Tidal track data for ID ${trackId}:`, error);
    throw error;
  }
}

/**
 * Get Tidal streaming URL with caching and proxy support
 * @param {string} tidalUrl - Original Tidal track URL
 * @param {string} quality - Quality preference (default: LOSSLESS)
 * @param {string|null} proxy - Optional proxy server
 * @returns {Promise<string>} Streaming URL
 */
async function getTidalStreamingUrl(tidalUrl, quality = 'LOSSLESS', proxy = null) {
  // Use cache for streaming URLs (short cache time since URLs may expire)
  const cacheKey = `tidal_stream_${encodeURIComponent(tidalUrl)}_${quality}`;

  const fetchFunction = async () => {
    let lastError;

    // Try with current endpoint first, then rotate if needed
    for (let attempt = 0; attempt < TIDAL_ENDPOINTS.length; attempt++) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: `${getCurrentTidalEndpoint()}/dl/tidal?url=${encodeURIComponent(tidalUrl)}&quality=${quality}`,
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Orbit-Music-App/1.0',
            'X-Forwarded-For': generateRandomIP(), // Help bypass rate limiting
          },
          timeout: 8000 // Reduced timeout for faster response
        };

        // Add proxy if provided
        if (proxy) {
          config.proxy = {
            protocol: proxy.startsWith('https') ? 'https' : 'http',
            host: proxy.split('://')[1].split(':')[0],
            port: parseInt(proxy.split(':')[2]) || 8080
          };
        }

        const response = await axios.request(config);

        if (response.data && (response.data.url || response.data.directUrl)) {
          return response.data.url || response.data.directUrl;
        }

        throw new Error('No streaming URL found in response');

      } catch (error) {
        lastError = error;

        // If rate limited, rotate endpoint and try again
        if (error.response && error.response.status === 429) {
          console.log(`Rate limited on ${getCurrentTidalEndpoint()}, rotating endpoint...`);
          rotateEndpoint();

          // Add longer delay before retry to respect rate limits
          await new Promise(resolve => setTimeout(resolve, 5000));
          continue;
        }

        // For other errors, don't retry
        break;
      }
    }

    throw lastError;
  };

  try {
    // Cache for 10 minutes to reduce API calls (streaming URLs are usually valid longer)
    return await getCachedData(cacheKey, fetchFunction, 10, CACHE_GROUPS.SONGS);
  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      throw new Error('Tidal streaming URL request timeout');
    }
    if (error.response && error.response.status === 429) {
      throw new Error('Tidal API rate limit exceeded (429)');
    }
    console.error('Error getting Tidal streaming URL:', error);
    throw error;
  }
}

/**
 * Generate random IP for X-Forwarded-For header to help bypass rate limiting
 */
function generateRandomIP() {
  return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
}

/**
 * Get Tidal album data (placeholder - shows message that albums not supported)
 * @param {string} albumId - Album ID
 * @returns {Promise<Object>} Error message
 */
async function getTidalAlbumData(albumId) {
  return {
    success: false,
    message: 'Albums are not supported with Tidal. Please use Saavn for albums.',
    unsupported: true
  };
}

/**
 * Get Tidal playlist data (placeholder - shows message that playlists not supported)
 * @param {string} playlistId - Playlist ID
 * @returns {Promise<Object>} Error message
 */
async function getTidalPlaylistData(playlistId) {
  return {
    success: false,
    message: 'Playlists are not supported with Tidal. Please use Saavn for playlists.',
    unsupported: true
  };
}

export {
  getTidalSearchSongData,
  getTidalTrackData,
  getTidalStreamingUrl,
  getTidalAlbumData,
  getTidalPlaylistData,
  getCurrentTidalEndpoint,
  rotateEndpoint,
  generateRandomIP
};
